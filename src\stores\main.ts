import { defineStore } from 'pinia';
import type { Book } from '../types/book';
import type { Item } from '../types/item';
import { useAuthStore } from './auth';
import { collection, onSnapshot, getDocs } from 'firebase/firestore';
import { db } from '../config/firebase';
import { buildApiUrl } from '../config/api';

interface State {
  books: Book[];
  selectedBookId: string | null;
  loading: boolean;
  error: string | null;
  isRealTimeEnabled: boolean;
  unsubscribers: (() => void)[];
  // Loading states for individual actions
  actionLoading: {
    addingBook: boolean;
    addingChapter: boolean;
    addingItem: boolean;
    updatingItem: boolean;
    deletingItem: boolean;
    deletingChapter: boolean;
    deletingBook: boolean;
  };
}

export const useMainStore = defineStore('main', {
  state: (): State => ({
    books: [],
    selectedBookId: null,
    loading: false,
    error: null,
    isRealTimeEnabled: false,
    unsubscribers: [],
    actionLoading: {
      addingBook: false,
      addingChapter: false,
      addingItem: false,
      updatingItem: false,
      deletingItem: false,
      deletingChapter: false,
      deletingBook: false,
    },
  }),

  actions: {
    // Enable polling-based updates (simpler than Firestore listeners for now)
    // Disable real-time listeners
    disableRealTimeUpdates() {
      this.unsubscribers.forEach(unsubscribe => unsubscribe());
      this.unsubscribers = [];
      this.isRealTimeEnabled = false;
      console.log('🔇 Real-time updates disabled');
    },

    // Initialize the store - non-blocking approach
    async initializeStore() {
      console.log('🚀 Initializing main store (non-blocking)...');
      const authStore = useAuthStore();

      // Don't wait for auth - let the auth state change handler deal with loading
      if (authStore.initialized) {
        // Auth is already ready, trigger the auth state change handler
        console.log('📚 Auth already initialized, triggering auth state change...');
        await this.onAuthStateChange(authStore.isAuthenticated);
      } else {
        console.log('⏳ Auth not ready yet, will load data when auth state changes');
        // The onAuthStateChange handler will load data when auth is ready
      }
    },

    // Called when user authentication state changes
    async onAuthStateChange(isAuthenticated: boolean) {
      console.log(`🔐 Auth state changed: ${isAuthenticated ? 'authenticated' : 'not authenticated'}`);

      if (isAuthenticated) {
        // User signed in - load books from Firestore
        console.log('📚 User signed in, loading books from Firestore...');
        // Clean up any existing listeners first
        this.cleanup();
        await this.loadBooksFromFirestore();
      } else {
        // User signed out - clear books and load static fallback
        console.log('🔒 User signed out, clearing books and loading static fallback...');
        this.cleanup();
        await this.loadBooksFromStatic();
      }
    },

    // Load books directly from Firestore with real-time updates (optimized)
    async loadBooksFromFirestore() {
      console.log('📚 Loading books from Firestore (optimized)...');
      this.loading = true;
      this.error = null;

      try {
        // Set up real-time listener for books collection
        const booksCollection = collection(db, 'books');

        // Use onSnapshot for real-time updates
        const unsubscribe = onSnapshot(booksCollection, async (snapshot) => {
          console.log('📡 Books collection changed, updating books...');

          // Process the snapshot directly for better real-time updates
          const books: Book[] = [];
          snapshot.docs.forEach((bookDoc) => {
            const bookData = bookDoc.data();
            books.push({
              id: bookDoc.id,
              title: bookData.title || '',
              description: bookData.description || '',
              chapters: [] // Start with empty chapters, load them progressively
            });
          });

          // Update books immediately
          this.books = books;
          this.loading = false;
          console.log(`✅ Real-time update: ${books.length} books loaded`);

          // Load chapters and items in background
          if (books.length > 0) {
            this.loadChaptersAndItems(books);
          }
        }, (error) => {
          console.error('❌ Firestore listener error:', error);
          // Fall back to static file if Firestore fails
          this.loadBooksFromStatic();
        });

        // Store the unsubscriber
        this.unsubscribers.push(unsubscribe);

        // The onSnapshot listener will handle initial data loading

      } catch (error) {
        console.error('❌ Failed to set up Firestore listener:', error);
        // Fall back to static file
        this.loadBooksFromStatic();
      }
    },

    // Load books first (fast), then chapters/items progressively
    async loadCompleteBookData() {
      console.log('🔄 Starting optimized book data loading...');
      this.loading = true;
      this.error = null;

      try {
        // First, load just the books quickly
        const books: Book[] = [];
        const booksCollection = collection(db, 'books');
        console.log('📖 Getting books collection...');
        const booksSnapshot = await getDocs(booksCollection);
        console.log(`📖 Found ${booksSnapshot.docs.length} book documents`);

        // Load books first to show them immediately
        for (const bookDoc of booksSnapshot.docs) {
          const bookData = bookDoc.data();
          books.push({
            id: bookData.id,
            title: bookData.title,
            description: bookData.description,
            chapters: [] // Start with empty chapters, load them progressively
          });
        }

        // Update UI immediately with books (empty chapters)
        this.books = books;
        this.loading = false;
        console.log(`✅ Loaded ${books.length} books (chapters loading in background)`);

        // Now load chapters and items in the background
        await this.loadChaptersAndItems(books);

      } catch (error) {
        console.error('❌ Failed to load book data:', error);
        this.error = String(error);
        this.loading = false;
        // Fall back to static file if Firestore fails
        console.log('🔄 Falling back to static file...');
        await this.loadBooksFromStatic();
      }
    },

    // Load chapters and items progressively
    async loadChaptersAndItems(books: Book[]) {
      console.log('📑 Loading chapters and items in background...');

      try {
        for (const book of books) {
          const bookRef = collection(db, 'books', book.id, 'chapters');
          const chaptersSnapshot = await getDocs(bookRef);

          for (const chapterDoc of chaptersSnapshot.docs) {
            const chapterData = chapterDoc.data();
            const chapter = {
              id: chapterDoc.id, // Use document ID instead of data.id
              title: chapterData.title || '',
              items: [] as Item[]
            };

            // Load items for this chapter
            const itemsRef = collection(db, 'books', book.id, 'chapters', chapter.id, 'items');
            const itemsSnapshot = await getDocs(itemsRef);

            for (const itemDoc of itemsSnapshot.docs) {
              const itemData = itemDoc.data();
              chapter.items.push({
                id: itemDoc.id, // Use document ID
                ...itemData
              } as Item);
            }

            book.chapters.push(chapter);
          }
        }

        // Update the store with complete data
        this.books = [...books]; // Trigger reactivity
        console.log(`✅ Background loading complete - loaded chapters and items`);

      } catch (error) {
        console.error('❌ Failed to load chapters/items:', error);
        // Don't fail completely - books are already loaded
      }
    },





    // Fallback method for unauthenticated users
    async loadBooksFromStatic() {
      console.log('📄 Setting empty books for unauthenticated user...');
      this.books = [];
      this.loading = false;
      this.error = null;
      console.log('✅ Set empty books array for unauthenticated user');
    },

    async addBook(book: { title: string; description: string }) {
      this.actionLoading.addingBook = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl('/api/books'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(book),
        });

        if (!response.ok) {
          throw new Error(`Failed to add book: ${response.status}`);
        }

        const newBook = await response.json();
        this.books.push(newBook);
        return newBook;
      } catch (error) {
        console.error('Error adding book:', error);
        // Fallback to local state only
        const bookCount = this.books.length + 1;
        const fallbackBook = {
          id: `book${bookCount}`,
          title: book.title,
          description: book.description,
          chapters: []
        };
        this.books.push(fallbackBook);
        return fallbackBook;
      } finally {
        this.actionLoading.addingBook = false;
      }
    },

    selectBook(id: string | null) {
      this.selectedBookId = id;
    },

    async addChapter(bookId: string, chapter: { title: string }) {
      this.actionLoading.addingChapter = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl(`/api/books/${bookId}/chapters`), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(chapter),
        });

        if (!response.ok) {
          throw new Error(`Failed to add chapter: ${response.status}`);
        }

        const newChapter = await response.json();
        const book = this.books.find(b => b.id === bookId);
        if (book) {
          book.chapters.push(newChapter);
        }
        return newChapter;
      } catch (error) {
        console.error('Error adding chapter:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapterCount = book ? book.chapters.length + 1 : 1;
        const fallbackChapter = {
          id: `${bookId}-ch${chapterCount}`,
          title: chapter.title,
          items: []
        };
        if (book) {
          book.chapters.push(fallbackChapter);
        }
        return fallbackChapter;
      } finally {
        this.actionLoading.addingChapter = false;
      }
    },

    async addItem(bookId: string, chapterId: string, item: Omit<Item, 'id'>) {
      this.actionLoading.addingItem = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl(`/api/books/${bookId}/chapters/${chapterId}/items`), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(item),
        });

        if (!response.ok) {
          throw new Error(`Failed to add item: ${response.status}`);
        }

        const newItem = await response.json();
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          chapter.items.push(newItem);
        }
        return newItem;
      } catch (error) {
        console.error('Error adding item:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        const itemCount = chapter ? chapter.items.length + 1 : 1;
        const fallbackItem = {
          id: `${chapterId}-item${itemCount}`,
          ...item
        };
        if (chapter) {
          chapter.items.push(fallbackItem);
        }
        return fallbackItem;
      } finally {
        this.actionLoading.addingItem = false;
      }
    },

    async updateItem(bookId: string, chapterId: string, itemId: string, item: Partial<Item>) {
      this.actionLoading.updatingItem = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl(`/api/books/${bookId}/chapters/${chapterId}/items/${itemId}`), {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(item),
        });

        if (!response.ok) {
          throw new Error(`Failed to update item: ${response.status}`);
        }

        const updatedItem = await response.json();
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items[itemIndex] = updatedItem;
          }
        }
        return updatedItem;
      } catch (error) {
        console.error('Error updating item:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items[itemIndex] = { ...chapter.items[itemIndex], ...item };
          }
        }
        return chapter?.items.find(i => i.id === itemId);
      } finally {
        this.actionLoading.updatingItem = false;
      }
    },

    getBook(id: string | undefined): Book | undefined {
      return this.books.find(book => book.id === id);
    },

    async deleteBook(bookId: string) {
      this.actionLoading.deletingBook = true;
      try {
        const response = await fetch(buildApiUrl(`/api/books/${bookId}`), {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error(`Failed to delete book: ${response.status}`);
        }

        // Remove from local state
        const bookIndex = this.books.findIndex(b => b.id === bookId);
        if (bookIndex !== -1) {
          this.books.splice(bookIndex, 1);
        }

        // Clear selection if deleted book was selected
        if (this.selectedBookId === bookId) {
          this.selectedBookId = null;
        }

        return true;
      } catch (error) {
        console.error('Error deleting book:', error);
        // Fallback to local state only
        const bookIndex = this.books.findIndex(b => b.id === bookId);
        if (bookIndex !== -1) {
          this.books.splice(bookIndex, 1);
        }
        if (this.selectedBookId === bookId) {
          this.selectedBookId = null;
        }
        return true;
      } finally {
        this.actionLoading.deletingBook = false;
      }
    },

    async deleteChapter(bookId: string, chapterId: string) {
      this.actionLoading.deletingChapter = true;
      try {
        const response = await fetch(buildApiUrl(`/api/books/${bookId}/chapters/${chapterId}`), {
          method: 'DELETE',
        });

        if (!response.ok) {
          throw new Error(`Failed to delete chapter: ${response.status}`);
        }

        // Remove from local state
        const book = this.books.find(b => b.id === bookId);
        if (book) {
          const chapterIndex = book.chapters.findIndex(c => c.id === chapterId);
          if (chapterIndex !== -1) {
            book.chapters.splice(chapterIndex, 1);
          }
        }

        return true;
      } catch (error) {
        console.error('Error deleting chapter:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        if (book) {
          const chapterIndex = book.chapters.findIndex(c => c.id === chapterId);
          if (chapterIndex !== -1) {
            book.chapters.splice(chapterIndex, 1);
          }
        }
        return true;
      } finally {
        this.actionLoading.deletingChapter = false;
      }
    },

    async deleteItem(bookId: string, chapterId: string, itemId: string) {
      this.actionLoading.deletingItem = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl(`/api/books/${bookId}/chapters/${chapterId}/items/${itemId}`), {
          method: 'DELETE',
          headers: {
            ...headers,
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to delete item: ${response.status}`);
        }

        // Remove from local state
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items.splice(itemIndex, 1);
          }
        }

        return true;
      } catch (error) {
        console.error('Error deleting item:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items.splice(itemIndex, 1);
          }
        }
        return true;
      } finally {
        this.actionLoading.deletingItem = false;
      }
    },



    // Cleanup method to call when store is destroyed
    cleanup() {
      this.disableRealTimeUpdates();
      console.log('🧹 Store cleanup completed');
    },
  },
});