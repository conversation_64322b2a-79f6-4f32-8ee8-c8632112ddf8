import { defineStore } from 'pinia';
import { collection, onSnapshot, query, orderBy } from 'firebase/firestore';
import { db } from '../config/firebase';
import type { ActionHistory } from '../types/actionHistory';
import { useAuthStore } from './auth';

interface HistoryState {
  history: ActionHistory[];
  loading: boolean;
  error: string | null;
  unsubscribe: (() => void) | null;
  newActionsAvailable: boolean;
  lastSeenCount: number;
}

export const useHistoryStore = defineStore('history', {
  state: (): HistoryState => ({
    history: [],
    loading: false,
    error: null,
    unsubscribe: null,
    newActionsAvailable: false,
    lastSeenCount: 0,
  }),

  actions: {
    subscribeToHistory() {
      if (this.unsubscribe) return;

      this.loading = true;
      const historyCollection = collection(db, 'actionHistory');
      const q = query(historyCollection, orderBy('timestamp', 'desc'));

      const authStore = useAuthStore();

      this.unsubscribe = onSnapshot(q, (snapshot) => {
        const newHistory = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        })) as ActionHistory[];

        // Check for new actions not performed by the current user
        if (this.history.length > 0) {
          const currentUserId = authStore.user?.id;
          const unseenActions = newHistory.filter(action => 
            !this.history.some(oldAction => oldAction.id === action.id) && 
            action.userId !== currentUserId
          );
          if (unseenActions.length > 0) {
            this.newActionsAvailable = true;
          }
        }

        this.history = newHistory;
        this.loading = false;
      }, (error) => {
        this.error = error.message;
        this.loading = false;
      });
    },

    markHistoryAsSeen() {
      this.newActionsAvailable = false;
      this.lastSeenCount = this.history.length;
    },

    unsubscribeFromHistory() {
      if (this.unsubscribe) {
        this.unsubscribe();
        this.unsubscribe = null;
      }
    },
  },
});
