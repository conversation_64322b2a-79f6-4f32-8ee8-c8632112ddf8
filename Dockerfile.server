# Multi-stage build for server with frontend
FROM node:18-alpine AS builder

# Install build dependencies
RUN apk add --no-cache \
    python3 \
    py3-pip \
    make \
    g++

# Set working directory
WORKDIR /app

# Copy package files
COPY package.json package-lock.json ./

# Install all dependencies (including dev dependencies for building)
RUN npm ci

# Copy source code
COPY . .

# Build the frontend
RUN npm run build

# Production stage
FROM node:18-alpine

# Install only the system dependencies needed for the server
RUN apk add --no-cache \
    python3 \
    py3-pip \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev

# Set working directory
WORKDIR /app

# Copy server-specific package.json
COPY server-package.json package.json

# Install only server dependencies
RUN npm install --only=production

# Copy server file and built frontend files from builder stage
COPY server.cjs ./
COPY --from=builder /app/dist ./public

# Expose port 8080
EXPOSE 8080

# Set environment variables
ENV NODE_ENV=production

# Start the server
CMD ["node", "server.cjs"]
