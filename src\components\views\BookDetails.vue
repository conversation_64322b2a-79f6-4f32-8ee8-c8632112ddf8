<template>
  <div class="h-full overflow-hidden">
    <!-- Loading State -->
    <div v-if="(store.loading || isInitializing) && !book" class="max-w-md mx-auto mt-16 border border-surface-300 rounded-xl">
      <div class="text-center p-6">
        <i class="pi pi-spin pi-spinner text-3xl text-primary-600 mb-4"></i>
        <h1 class="text-xl font-bold text-surface-800 mb-2">Loading Books</h1>
        <p class="text-surface-600 text-sm">
          Please wait while we load your textbooks...
        </p>
      </div>
    </div>

    <!-- No Books Available State -->
    <div v-else-if="!store.loading && !isInitializing && store.books.length === 0" class="max-w-md mx-auto mt-16 border border-surface-300 rounded-xl">
      <div class="text-center p-6">
        <i class="pi pi-book text-3xl text-surface-400 mb-4"></i>
        <h1 class="text-xl font-bold text-surface-800 mb-2">No Books Available</h1>
        <p class="text-surface-600 text-sm">
          There are no textbooks available at the moment. Please contact your administrator or check back later.
        </p>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!book" class="max-w-md mx-auto mt-16 border border-surface-300 rounded-xl">
      <div class="text-center p-6">
        <Avatar
          icon="pi pi-book"
          size="large"
          class="mb-4 bg-primary-100 text-primary-600"
        />
        <h1 class="text-xl font-bold text-surface-800 mb-2">No Book Selected</h1>
        <p class="text-surface-600 text-sm">
          Please select a book from the sidebar to view its details and manage chapters.
        </p>
      </div>
    </div>

    <!-- Book Details Content -->
    <div v-else class="h-full flex flex-col overflow-hidden p-2">
      <!-- Header -->
      <div class="flex-shrink-0 mb-2 border border-surface-300 rounded-xl">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3 p-3">
          <div class="flex items-center gap-3">
            <Avatar
              icon="pi pi-book"
              size="normal"
              class="bg-gradient-to-br from-primary-500 to-purple-600 text-white"
            />
            <div>
              <h1 class="text-lg font-bold text-surface-800">{{ book.title }}</h1>
            </div>
          </div>
          <div class="flex gap-1">
            <Button
              v-if="authStore.canDeleteBooks"
              label="Delete Book"
              icon="pi pi-trash"
              severity="danger"
              outlined
              @click="confirmDeleteBook"
              class="transition-all duration-200"
              size="small"
            />
            <Button
              v-if="selectedChapterId && authStore.canDeleteChapters"
              label="Delete Chapter"
              icon="pi pi-trash"
              severity="danger"
              @click="confirmDeleteChapter"
              class="transition-all duration-200"
              size="small"
            />
          </div>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 xl:grid-cols-12 gap-2 flex-1 min-h-0">
        <!-- Chapters & Items Panel -->
        <div class="xl:col-span-4 flex flex-col gap-2 min-h-0">
          <!-- Chapters Panel -->
          <Panel class="flex-1 !rounded-xl flex flex-col min-h-0">
            <template #header>
              <div class="flex items-center gap-2 w-full p-2">
                <Avatar
                  icon="pi pi-list"
                  size="small"
                  class="bg-primary-100 text-primary-600"
                />
                <span class="font-semibold text-sm">Chapters</span>
                <div class="ml-auto">
                  <Button
                    v-if="authStore.canCreateChapters"
                    label="Add Chapter"
                    icon="pi pi-plus"
                    size="small"
                    outlined
                    :loading="store.actionLoading.addingChapter"
                    @click="openAddChapterDialog"
                    class="transition-all duration-200"
                  />
                </div>
              </div>
            </template>
            <div v-if="!book.chapters || book.chapters.length === 0" class="text-center py-12">
                <Avatar
                  icon="pi pi-book"
                  size="xlarge"
                  class="mb-6 bg-surface-100 text-surface-400"
                />
                <h3 class="text-lg font-semibold text-surface-700 mb-2">No chapters available</h3>
                <p class="text-surface-500 mb-6">Create your first chapter to get started</p>
              </div>
            <div v-else class="flex-1 overflow-y-auto min-h-0 p-2 space-y-1">
              <div
                v-for="chapter in book.chapters"
                :key="chapter.id"
                @click="selectChapter(chapter.id)"
                class="p-3 rounded-lg cursor-pointer transition-all duration-200 flex items-center gap-3"
                :class="{
                  'bg-primary-500/10 text-primary-700 font-semibold': selectedChapterId === chapter.id,
                  'hover:bg-surface-100': selectedChapterId !== chapter.id
                }"
              >
                <i class="pi pi-book text-surface-400"></i>
                <span>{{ chapter.title }}</span>
              </div>
            </div>
          </Panel>

          <!-- Items Panel -->
          <Panel v-if="selectedChapter" class="flex-1 !rounded-xl flex flex-col min-h-0">
             <template #header>
              <div class="flex items-center gap-2 w-full p-2">
                <Avatar
                  :icon="getItemIcon(selectedItem?.type || '')"
                  size="small"
                  class="bg-purple-100 text-purple-600"
                />
                <span class="font-semibold text-sm">Items in {{ selectedChapter.title }}</span>
                <div class="ml-auto">
                  <Button
                    v-if="authStore.canEdit"
                    label="Add Item"
                    icon="pi pi-plus-circle"
                    size="small"
                    @click="openItemDialog"
                    class="transition-all duration-200 bg-purple-600 text-white border-purple-600"
                  />
                </div>
              </div>
            </template>
            <div v-if="!selectedChapter.items || selectedChapter.items.length === 0" class="text-center py-12">
              <Avatar
                icon="pi pi-file"
                size="xlarge"
                class="mb-6 bg-surface-100 text-surface-400"
              />
              <h3 class="text-lg font-semibold text-surface-700 mb-2">No items in this chapter</h3>
              <p class="text-surface-500 mb-6">Click "Add Item" to create the first one.</p>
            </div>
            <div v-else class="flex-1 overflow-y-auto min-h-0 p-2 space-y-1">
              <div
                v-for="item in selectedChapter.items"
                :key="item.id"
                @click="selectItem(item.id)"
                class="p-3 rounded-lg cursor-pointer transition-all duration-200 flex items-center gap-3"
                :class="{
                  'bg-primary-500/10 text-primary-700 font-semibold': selectedItemId === item.id,
                  'hover:bg-surface-100': selectedItemId !== item.id
                }"
              >
                <i :class="getItemIcon(item.type)" class="text-surface-400"></i>
                <span>{{ item.title || item.question || 'Untitled' }}</span>
              </div>
            </div>
          </Panel>
        </div>

        <!-- Main Content Area -->
        <div class="xl:col-span-8 flex flex-col min-h-0">
          <!-- QR Code and Item Preview with Splitter -->
          <div v-if="selectedItemId && !editingItemId" class="flex-1 flex flex-col min-h-0">
            <Splitter layout="vertical" class="h-full !rounded-xl !border-surface-300 overflow-hidden">
              <!-- QR Code Panel -->
              <SplitterPanel :size="60" :minSize="30" class="flex flex-col">
                <div class="flex items-center gap-2 p-3 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-surface-300">
                  <Avatar
                    icon="pi pi-qrcode"
                    size="small"
                    class="bg-green-100 text-green-600"
                  />
                  <h2 class="text-sm font-bold text-surface-800">Item QR Code</h2>
                </div>
                <div class="p-2 flex-1 overflow-y-auto">
                  <QRCodeDisplay
                    :bookId="book!.id"
                    :chapterId="selectedChapterId!"
                    :itemId="selectedItemId"
                    :onDelete="() => confirmDeleteItem()"
                    :onEdit="() => startEditingItem()"
                  />
                </div>
              </SplitterPanel>

              <!-- Item Preview Panel -->
              <SplitterPanel :size="40" :minSize="20" class="flex flex-col">
                <div class="flex items-center gap-2 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-surface-300">
                  <Avatar
                    icon="pi pi-eye"
                    size="small"
                    class="bg-blue-100 text-blue-600"
                  />
                  <h2 class="text-sm font-bold text-surface-800">Item Preview</h2>
                  <div v-if="authStore.user?.role !== 'viewer'" class="ml-auto flex gap-2">
                    <Button
                      icon="pi pi-pencil"
                      size="small"
                      outlined
                      @click="startEditingItem"
                      v-tooltip="'Edit Item'"
                      class="transition-all duration-200"
                    />
                  </div>
                </div>
                <div class="p-4 flex-1 overflow-y-auto">
                  <ItemPreview :item="selectedItem" />
                </div>
              </SplitterPanel>
            </Splitter>
          </div>

          <!-- Chapter Selected State -->
          <div v-else-if="selectedChapterId" class="flex-1 flex flex-col min-h-0 h-full">
            <div class="flex items-center justify-center h-full text-center">
               <div class="text-center max-w-md">
                <Avatar
                  icon="pi pi-arrow-up"
                  size="xlarge"
                  class="mb-6 bg-green-100 text-green-600"
                />
                <h3 class="text-xl font-semibold text-surface-700 mb-3">Select an Item</h3>
                <p class="text-surface-500 mb-6">
                  Select an item from the list to view its QR code and preview.
                </p>
              </div>
            </div>
          </div>

          <!-- Default State -->
          <div v-else>
            <div class="text-center py-16">
              <Avatar
                icon="pi pi-arrow-left"
                size="xlarge"
                class="mb-6 bg-surface-100 text-surface-400"
              />
              <h3 class="text-xl font-semibold text-surface-700 mb-2">Get Started</h3>
              <p class="text-surface-500">Select a chapter from the list to begin working.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
      <Dialog v-model:visible="showAddChapterDialog" header="Add Chapter" :style="{ width: '25vw' }">
        <InputText v-model="newChapterTitle" placeholder="Chapter Title" class="w-full" />
        <template #footer>
          <Button label="Cancel" class="p-button-text" @click="showAddChapterDialog = false" />
          <Button label="Save" :loading="store.actionLoading.addingChapter" @click="saveChapter" />
        </template>
      </Dialog>

      <!-- Delete Confirmation Dialog -->
      <Dialog v-model:visible="showDeleteDialog" header="Confirm Delete" :style="{ width: '30vw' }">
        <p class="mb-4">{{ deleteMessage }}</p>
        <template #footer>
          <Button label="Cancel" class="p-button-text" @click="showDeleteDialog = false" />
          <Button
            label="Delete"
            severity="danger"
            :loading="store.actionLoading.deletingItem || store.actionLoading.deletingChapter || store.actionLoading.deletingBook"
            @click="performDelete"
          />
        </template>
      </Dialog>

      <!-- Item Form Dialog -->
      <ItemFormDialog
        v-model:visible="showItemDialog"
        :chapterId="selectedChapterId || undefined"
        @itemCreated="handleItemCreated"
      />

      <!-- Item Edit Dialog -->
      <ItemEditDialog
        v-model:visible="showEditItemDialog"
        :item="selectedItem || undefined"
        :bookId="book?.id"
        :chapterId="selectedChapterId || undefined"
        @itemUpdated="handleItemUpdated"
      />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useBookStore } from '../../stores/book';
import { useAuthStore } from '../../stores/auth';

import ItemFormDialog from '../common/ItemFormDialog.vue';
import ItemEditDialog from '../common/ItemEditDialog.vue';
import QRCodeDisplay from '../common/QRCodeDisplay.vue';
import Dialog from 'primevue/dialog';
import Button from 'primevue/button';
import InputText from 'primevue/inputtext';
import Panel from 'primevue/panel';
import Avatar from 'primevue/avatar';
import Chip from 'primevue/chip';
import Splitter from 'primevue/splitter';
import SplitterPanel from 'primevue/splitterpanel';
import ItemPreview from '../common/ItemPreview.vue';

const store = useBookStore();
const authStore = useAuthStore();
const route = useRoute();
const router = useRouter();

const book = computed(() => store.getBook(route.params.id as string));
const isInitializing = ref(true);

// State
const selectedChapterId = ref<string | null>(null);
const selectedItemId = ref<string | null>(null);
const editingItemId = ref<string | null>(null);

// Dialogs
const showAddChapterDialog = ref(false);
const newChapterTitle = ref('');
const showDeleteDialog = ref(false);
const deleteMessage = ref('');
const deleteAction = ref<(() => Promise<void>) | null>(null);
const showItemDialog = ref(false);
const showEditItemDialog = ref(false);

// Computed Properties
const selectedChapter = computed(() => {
  if (!book.value || !selectedChapterId.value) return null;
  return book.value.chapters.find(c => c.id === selectedChapterId.value) || null;
});

const selectedItem = computed(() => {
  if (!selectedChapter.value || !selectedItemId.value) return null;
  return selectedChapter.value.items.find(i => i.id === selectedItemId.value) || null;
});

// Book loading and routing logic
watch([() => store.loading, () => store.books, () => route.params.id], async () => {
  if (store.loading) {
    isInitializing.value = true;
    return;
  }
  if (store.books.length === 0 && !store.loading) {
     isInitializing.value = false;
     return;
  }

  const bookId = route.params.id as string;

  if (!bookId && store.books.length > 0) {
    await router.push(`/book/${store.books[0].id}`);
    return;
  }

  if (bookId && !store.getBook(bookId)) {
    await router.push('/');
    return;
  }
  
  if (bookId && store.getBook(bookId)) {
    isInitializing.value = false;
  }
}, { immediate: true });

// When the book changes, clear selections
watch(book, (newBook, oldBook) => {
  if (newBook?.id !== oldBook?.id) {
    selectedChapterId.value = null;
    selectedItemId.value = null;
    editingItemId.value = null;
  }
});

onMounted(async () => {
  await store.initializeStore();
});

// Methods
const selectChapter = (chapterId: string) => {
  selectedChapterId.value = chapterId;
  selectedItemId.value = null; // Reset item selection when chapter changes
  editingItemId.value = null;
};

const selectItem = (itemId: string) => {
  selectedItemId.value = itemId;
  editingItemId.value = null;
};

const getItemIcon = (itemType: string): string => {
  const iconMap: Record<string, string> = {
    'question': 'pi pi-fw pi-question-circle',
    'test': 'pi pi-fw pi-list-check',
    'text': 'pi pi-fw pi-info-circle',
    'image': 'pi pi-fw pi-image',
    'link': 'pi pi-fw pi-link',
    'map': 'pi pi-fw pi-map',
    'diagram': 'pi pi-fw pi-sitemap',
    'timed-question': 'pi pi-fw pi-clock'
  };
  return iconMap[itemType] || 'pi pi-fw pi-file';
};

// Chapter Actions
const openAddChapterDialog = () => {
  newChapterTitle.value = '';
  showAddChapterDialog.value = true;
};

const saveChapter = async () => {
  if (book.value && newChapterTitle.value) {
    await store.addChapter(book.value.id, { title: newChapterTitle.value });
    showAddChapterDialog.value = false;
  }
};

// Item Actions
const openItemDialog = () => {
  showItemDialog.value = true;
};

const handleItemCreated = (newItem: any) => {
  if (newItem) {
    selectItem(newItem.id);
  }
};

const startEditingItem = () => {
  if (selectedItem.value) {
    editingItemId.value = selectedItem.value.id;
    showEditItemDialog.value = true;
  }
};

const handleItemUpdated = (updatedItem: any) => {
  editingItemId.value = null;
  if (updatedItem) {
    selectItem(updatedItem.id);
  }
};

// Delete Actions
const confirmDeleteBook = () => {
  if (!book.value) return;
  deleteMessage.value = `Are you sure you want to delete the book "${book.value.title}" and all its content? This action cannot be undone.`;
  deleteAction.value = async () => {
    await store.deleteBook(book.value!.id);
    router.push('/');
  };
  showDeleteDialog.value = true;
};

const confirmDeleteChapter = () => {
  if (!selectedChapter.value || !book.value) return;
  const chapterTitle = selectedChapter.value.title;
  deleteMessage.value = `Are you sure you want to delete the chapter "${chapterTitle}" and all its items?`;
  deleteAction.value = async () => {
    await store.deleteChapter(book.value!.id, selectedChapterId.value!);
    selectedChapterId.value = null;
  };
  showDeleteDialog.value = true;
};

const confirmDeleteItem = () => {
  if (!selectedItem.value || !book.value || !selectedChapterId.value) return;
  const itemTitle = selectedItem.value.title || 'Untitled';
  deleteMessage.value = `Are you sure you want to delete the item "${itemTitle}"?`;
  deleteAction.value = async () => {
    await store.deleteItem(book.value!.id, selectedChapterId.value!, selectedItemId.value!);
    selectedItemId.value = null;
  };
  showDeleteDialog.value = true;
};

const performDelete = async () => {
  if (deleteAction.value) {
    await deleteAction.value();
  }
  showDeleteDialog.value = false;
  deleteAction.value = null;
};

</script>
