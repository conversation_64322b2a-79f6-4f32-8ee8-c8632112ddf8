<template>
  <div class="h-full overflow-hidden">
    <!-- Loading State -->
    <div v-if="(store.loading || isInitializing) && !book" class="max-w-md mx-auto mt-16 border border-surface-300 rounded-xl">
      <div class="text-center p-6">
        <i class="pi pi-spin pi-spinner text-3xl text-primary-600 mb-4"></i>
        <h1 class="text-xl font-bold text-surface-800 mb-2">Loading Books</h1>
        <p class="text-surface-600 text-sm">
          Please wait while we load your textbooks...
        </p>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!book" class="max-w-md mx-auto mt-16 border border-surface-300 rounded-xl">
      <div class="text-center p-6">
        <Avatar
          icon="pi pi-book"
          size="large"
          class="mb-4 bg-primary-100 text-primary-600"
        />
        <h1 class="text-xl font-bold text-surface-800 mb-2">No Book Selected</h1>
        <p class="text-surface-600 text-sm">
          Please select a book from the sidebar to view its details and manage chapters.
        </p>
      </div>
    </div>

    <!-- Book Details Content -->
    <div v-else class="h-full flex flex-col overflow-hidden p-2">
      <!-- Header -->
      <div class="flex-shrink-0 mb-2 border border-surface-300 rounded-xl">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-3 p-3">
          <div class="flex items-center gap-3">
            <Avatar
              icon="pi pi-book"
              size="normal"
              class="bg-gradient-to-br from-primary-500 to-purple-600 text-white"
            />
            <div>
              <h1 class="text-lg font-bold text-surface-800">{{ book.title }}</h1>
            </div>
          </div>
          <div class="flex gap-1">
            <Button
              v-if="authStore.canDeleteBooks"
              label="Delete Book"
              icon="pi pi-trash"
              severity="danger"
              outlined
              @click="confirmDeleteBook"
              class="transition-all duration-200"
              size="small"
            />
            <Button
              v-if="selectedChapterId && authStore.canDeleteChapters"
              label="Delete Chapter"
              icon="pi pi-trash"
              severity="danger"
              @click="deleteSelectedChapter"
              class="transition-all duration-200"
              size="small"
            />
          </div>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 xl:grid-cols-12 gap-2 flex-1 min-h-0">
        <!-- Chapters & Items Panel -->
        <div class="xl:col-span-4 flex flex-col min-h-0">
          <Panel class="flex-1 !rounded-xl flex flex-col min-h-0">
            <template #header>
              <div class="flex items-center gap-2 w-full p-2">
                <Avatar
                  icon="pi pi-list"
                  size="small"
                  class="bg-primary-100 text-primary-600"
                />
                <span class="font-semibold text-sm">Chapters & Items</span>
                <div class="ml-auto flex gap-2">
                  <Button
                    v-if="authStore.canCreateChapters"
                    label="Add Chapter"
                    icon="pi pi-plus"
                    size="small"
                    outlined
                    :loading="store.actionLoading.addingChapter"
                    @click="openAddChapterDialog"
                    class="transition-all duration-200"
                  />
                  <Button
                    v-if="authStore.canEdit"
                    :label="selectedChapterId ? 'Add Item' : 'Add Item (Select Chapter)'"
                    icon="pi pi-plus-circle"
                    size="small"
                    :outlined="!selectedChapterId"
                    :disabled="!selectedChapterId"
                    :loading="store.actionLoading.addingItem"
                    @click="openItemDialog"
                    class="transition-all duration-200"
                    :class="{ 'bg-purple-600 text-white border-purple-600': selectedChapterId }"
                  />
                </div>
              </div>
            </template>

            <!-- Empty State -->
            <div v-if="treeData.length === 0" class="text-center py-12">
              <Avatar
                icon="pi pi-book"
                size="xlarge"
                class="mb-6 bg-surface-100 text-surface-400"
              />
              <h3 class="text-lg font-semibold text-surface-700 mb-2">No chapters available</h3>
              <p class="text-surface-500 mb-6">Create your first chapter to get started</p>
              <Button
                v-if="authStore.canCreateChapters"
                label="Add Chapter"
                icon="pi pi-plus"
                :loading="store.actionLoading.addingChapter"
                @click="openAddChapterDialog"
                class="transition-all duration-200"
              />
            </div>

            <!-- Tree Component -->
            <div v-else class="flex-1 overflow-y-auto min-h-0">
              <Tree
                :key="treeKey"
                :value="treeData"
                selectionMode="single"
                v-model:selectionKeys="selectedKey"
                v-model:expandedKeys="expandedKeys"
                @node-select="onNodeSelect"
                @node-unselect="onNodeUnselect"
                @node-contextmenu="onNodeContextMenu"
                class="border-0 p-0"
              />
            </div>
          </Panel>
        </div>

        <!-- Main Content Area -->
        <div class="xl:col-span-8 flex flex-col min-h-0">
          <!-- QR Code and Item Preview with Splitter -->
          <div v-if="selectedItemId && !editingItemId" class="flex-1 flex flex-col min-h-0">
            <Splitter layout="vertical" class="h-full !rounded-xl !border-surface-300 overflow-hidden">
              <!-- QR Code Panel -->
              <SplitterPanel :size="60" :minSize="30" class="flex flex-col">
                <div class="flex items-center gap-2 p-3 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-surface-300">
                  <Avatar
                    icon="pi pi-qrcode"
                    size="small"
                    class="bg-green-100 text-green-600"
                  />
                  <h2 class="text-sm font-bold text-surface-800">Item QR Code</h2>
                </div>
                <div class="p-2 flex-1 overflow-y-auto">
                  <QRCodeDisplay
                    :bookId="book!.id"
                    :chapterId="selectedChapterId!"
                    :itemId="selectedItemId"
                    :onDelete="() => deleteSelectedItem()"
                    :onEdit="() => startEditingItem()"
                  />
                </div>
              </SplitterPanel>

              <!-- Item Preview Panel -->
              <SplitterPanel :size="40" :minSize="20" class="flex flex-col">
                <div class="flex items-center gap-2 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-surface-300">
                  <Avatar
                    icon="pi pi-eye"
                    size="small"
                    class="bg-blue-100 text-blue-600"
                  />
                  <h2 class="text-sm font-bold text-surface-800">Item Preview</h2>
                  <div v-if="authStore.user?.role !== 'viewer'" class="ml-auto flex gap-2">
                    <Button
                      icon="pi pi-pencil"
                      size="small"
                      outlined
                      @click="startEditingItem"
                      v-tooltip="'Edit Item'"
                      class="transition-all duration-200"
                    />
                  </div>
                </div>
                <div class="p-4 flex-1 overflow-y-auto">
                  <ItemPreview :item="selectedItem" />
                </div>
              </SplitterPanel>
            </Splitter>
          </div>

          <!-- Chapter Selected State -->
          <div v-else-if="selectedChapterId" class="flex-1 flex flex-col min-h-0 h-full">
            <div class="flex items-center gap-2 p-3 bg-gradient-to-r from-green-50 to-emerald-50 border-b border-surface-300">
              <Avatar
                icon="pi pi-check-circle"
                size="small"
                class="bg-green-100 text-green-600"
              />
              <h2 class="text-sm font-bold text-surface-800">Chapter Selected</h2>
              <div class="ml-auto">
                <Chip
                  :label="getSelectedChapterTitle()"
                  icon="pi pi-bookmark"
                  class="bg-green-100 text-green-800 border-green-200"
                />
              </div>
            </div>
            <div class="p-6 h-full flex items-center justify-center">
              <div class="text-center max-w-md">
                <Avatar
                  icon="pi pi-folder-open"
                  size="xlarge"
                  class="mb-6 bg-green-100 text-green-600"
                />
                <h3 class="text-xl font-semibold text-surface-700 mb-3">Chapter Ready</h3>
                <p class="text-surface-500 mb-6">
                  Use the "Add Item" button above to create content for this chapter.
                </p>
                <div class="text-surface-400 text-sm">
                  Available content types: Questions, Tests, Text, Images, Links, Maps, Diagrams
                </div>
              </div>
            </div>
          </div>

          <!-- Default State -->
          <div v-else>
            <div class="text-center py-16">
              <Avatar
                icon="pi pi-arrow-left"
                size="xlarge"
                class="mb-6 bg-surface-100 text-surface-400"
              />
              <h3 class="text-xl font-semibold text-surface-700 mb-2">Get Started</h3>
              <p class="text-surface-500">Select a chapter or item from the sidebar to begin working</p>
            </div>
          </div>
        </div>
      </div>
    </div>
      <Dialog v-model:visible="showAddChapterDialog" header="Add Chapter" :style="{ width: '25vw' }">
        <InputText v-model="newChapterTitle" placeholder="Chapter Title" class="w-full" />
        <template #footer>
          <Button label="Cancel" class="p-button-text" @click="showAddChapterDialog = false" />
          <Button label="Save" :loading="store.actionLoading.addingChapter" @click="saveChapter" />
        </template>
      </Dialog>

      <!-- Delete Confirmation Dialog -->
      <Dialog v-model:visible="showDeleteDialog" header="Confirm Delete" :style="{ width: '30vw' }">
        <p class="mb-4">{{ deleteMessage }}</p>
        <template #footer>
          <Button label="Cancel" class="p-button-text" @click="showDeleteDialog = false" />
          <Button
            label="Delete"
            severity="danger"
            :loading="store.actionLoading.deletingItem || store.actionLoading.deletingChapter || store.actionLoading.deletingBook"
            @click="performDelete"
          />
        </template>
      </Dialog>

      <!-- Item Form Dialog -->
      <ItemFormDialog
        v-model:visible="showItemDialog"
        :chapterId="selectedChapterId || undefined"
        @itemCreated="handleItemCreated"
      />

      <!-- Item Edit Dialog -->
      <ItemEditDialog
        v-model:visible="showEditItemDialog"
        :item="selectedItem || undefined"
        :bookId="book?.id"
        :chapterId="selectedChapterId || undefined"
        @itemUpdated="handleItemUpdated"
      />
    </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useBookStore } from '../../stores/book';
import { useAuthStore } from '../../stores/auth';

import type { TreeNode } from 'primevue/treenode';
import ItemFormDialog from '../common/ItemFormDialog.vue';
import ItemEditDialog from '../common/ItemEditDialog.vue';
import QRCodeDisplay from '../common/QRCodeDisplay.vue';
import Dialog from 'primevue/dialog';
import Button from 'primevue/button';
import Tree from 'primevue/tree';
import InputText from 'primevue/inputtext';
import Panel from 'primevue/panel';
import Avatar from 'primevue/avatar';
import Chip from 'primevue/chip';
import Splitter from 'primevue/splitter';
import SplitterPanel from 'primevue/splitterpanel';
import ItemPreview from '../common/ItemPreview.vue';

import type { TreeSelectionKeys } from 'primevue/tree';

const store = useBookStore();
const authStore = useAuthStore();
const route = useRoute();
const router = useRouter();
const book = computed(() => store.getBook(route.params.id as string));

// Handle book loading and routing
const isInitializing = ref(true);

// Watch for book data and handle routing
watch([() => store.loading, () => store.books, () => route.params.id], async () => {
  // If we're still loading, wait
  if (store.loading) {
    isInitializing.value = true;
    return;
  }

  // Wait for books to be loaded before making routing decisions
  if (store.books.length === 0) {
    isInitializing.value = true;
    return;
  }

  const bookId = route.params.id as string;

  // If we're on the home route (no book ID) and we have books, select the first one
  if (!bookId) {
    console.log('🏠 On home route with books available, selecting first book...');
    await router.push(`/book/${store.books[0].id}`);
    return;
  }

  // If we have a book ID but the book doesn't exist, redirect to home
  if (bookId && !book.value) {
    console.log(`📖 Book ${bookId} not found, redirecting to home...`);
    await router.push('/');
    return;
  }

  // If we have a valid book, we're done initializing
  if (bookId && book.value) {
    console.log(`📖 Successfully loaded book ${bookId}: ${book.value.title}`);
    isInitializing.value = false;
  }
}, { immediate: true });

// Initialize the store when component mounts
onMounted(async () => {
  console.log('📖 BookDetails mounted, initializing...');
  await store.initializeStore();
});
const selectedItem = computed(() => {
  if (!selectedItemId.value || !book.value) return null;
  const chapter = book.value.chapters.find(c => c.id === selectedChapterId.value);
  return chapter?.items.find(i => i.id === selectedItemId.value) || null;
});
const selectedKey = ref<TreeSelectionKeys | undefined>(undefined);
const expandedKeys = ref<TreeSelectionKeys>({});
const selectedChapterId = ref<string | null>(null);
const selectedItemId = ref<string | null>(null);
const editingItemId = ref<string | null>(null);
const showAddChapterDialog = ref(false);
const newChapterTitle = ref('');

// Use ref for tree data and manually update it
const treeData = ref<TreeNode[]>([]);

// Function to update tree data
const updateTreeData = () => {
  if (!book.value) {
    console.log('🌳 No book selected, clearing tree');
    treeData.value = [];
    return;
  }

  console.log('🌳 Updating tree data for book:', book.value.title, 'chapters:', book.value.chapters?.length);

  // This will manually update when called
  const chapters = Array.isArray(book.value.chapters) ? book.value.chapters : [];

  console.log('🌳 Detailed chapter analysis:');
  chapters.forEach((chapter, chIndex) => {
    console.log(`  Chapter ${chIndex}: "${chapter.title}" (id: ${chapter.id})`);
    console.log(`    Items array:`, chapter.items);
    console.log(`    Items count:`, Array.isArray(chapter.items) ? chapter.items.length : 'NOT_ARRAY');

    if (Array.isArray(chapter.items)) {
      chapter.items.forEach((item, itemIndex) => {
        console.log(`      Item ${itemIndex}:`, {
          id: item.id,
          title: item.title,
          type: item.type,
          fullObject: item
        });
      });
    }
  });

  const nodes = chapters.map(chapter => ({
    key: chapter.id,
    label: chapter.title,
    data: `Chapter: ${chapter.title}`,
    icon: 'pi pi-fw pi-book',
    selectable: true,
    children: Array.isArray(chapter.items) ? chapter.items.map(item => {
      console.log(`🌳 Creating tree node for item:`, { id: item.id, title: item.title, type: item.type });
      return {
        key: item.id,
        label: item.title || item.question || 'Untitled',
        data: `Item: ${item.type}`,
        icon: getItemIcon(item.type),
        selectable: true,
      };
    }) : [],
  }));

  console.log('🌳 Final tree nodes:', nodes);
  console.log('🌳 Tree structure:', nodes.map(node => ({
    chapter: node.label,
    childrenCount: node.children?.length || 0,
    children: node.children?.map(child => ({ key: child.key, label: child.label }))
  })));

  treeData.value = nodes;
  console.log('🌳 Tree data updated in ref');
};

// Create a reactive key that changes when tree data should update
const treeKey = computed(() => {
  if (!book.value) return 'no-book';

  const chapters = book.value.chapters || [];
  const itemCounts = chapters.map(ch => ch.items?.length || 0).join(',');
  const itemIds = chapters.map(ch => ch.items?.map(i => i.id).join(':')).join('|');

  const key = `${book.value.id}-${chapters.length}-${itemCounts}-${itemIds}`;
  console.log('🔑 Tree key updated:', key);

  // Update tree data when key changes
  nextTick(() => {
    updateTreeData();
  });

  return key;
});

// Helper function to get item icon (moved from NodeService)
const getItemIcon = (itemType: string): string => {
  const iconMap: Record<string, string> = {
    'question': 'pi pi-fw pi-question-circle',
    'test': 'pi pi-fw pi-list-check',
    'text': 'pi pi-fw pi-info-circle',
    'image': 'pi pi-fw pi-image',
    'link': 'pi pi-fw pi-link',
    'map': 'pi pi-fw pi-map',
    'diagram': 'pi pi-fw pi-sitemap',
    'timed-question': 'pi pi-fw pi-clock'
  };
  return iconMap[itemType] || 'pi pi-fw pi-file';
};

// Delete functionality
const showDeleteDialog = ref(false);
const deleteMessage = ref('');
const nodeToDelete = ref<TreeNode | null>(null);

// Item dialog functionality
const showItemDialog = ref(false);
const showEditItemDialog = ref(false);

// Function to expand all tree nodes
const expandAllNodes = (nodes: TreeNode[]): TreeSelectionKeys => {
  const expanded: TreeSelectionKeys = {};

  const expandNode = (node: TreeNode) => {
    if (node.children && node.children.length > 0) {
      expanded[node.key!] = true;
      node.children.forEach(expandNode);
    }
  };

  nodes.forEach(expandNode);
  return expanded;
};

// Function to refresh tree data (now just expands nodes since treeData is computed)
const refreshTreeData = async () => {
  if (book.value) {
    console.log('🔄 Refreshing tree data for book:', book.value.title, 'chapters:', book.value.chapters?.length);

    const currentSelectedKey = selectedKey.value;
    const currentChapterId = selectedChapterId.value;
    const currentItemId = selectedItemId.value;

    // Tree data is now computed automatically, just expand nodes
    expandedKeys.value = expandAllNodes(treeData.value);
    console.log('🔄 Tree nodes expanded, total nodes:', treeData.value.length);

    // Restore selection if it still exists
    if (currentItemId && book.value.chapters.some(ch => ch.items.some(item => item.id === currentItemId))) {
      selectedKey.value = currentSelectedKey;
      selectedChapterId.value = currentChapterId;
      selectedItemId.value = currentItemId;
    } else if (currentChapterId && book.value.chapters.some(ch => ch.id === currentChapterId)) {
      selectedKey.value = currentSelectedKey;
      selectedChapterId.value = currentChapterId;
      selectedItemId.value = null;
    } else if (currentSelectedKey) {
      // If neither item nor chapter exists anymore, clear selection
      clearSelection();
    }
  }
};

// Helper function to clear all selections
const clearSelection = () => {
  selectedKey.value = undefined;
  selectedChapterId.value = null;
  selectedItemId.value = null;
  editingItemId.value = null; // Also clear editing state
};

// Watch for book changes to expand tree and clear selections
watch(
  book,
  (newBook) => {
    console.log('📖 Book changed to:', newBook?.title);

    if (newBook) {
      // Update tree data first
      updateTreeData();

      // Expand all nodes when book changes
      nextTick(() => {
        expandedKeys.value = expandAllNodes(treeData.value);
        console.log('🌳 Tree expanded for new book');
      });
    } else {
      treeData.value = [];
      expandedKeys.value = {};
    }

    // Clear selections when book changes to prevent 404 errors
    clearSelection();
  },
  { immediate: true }
);

// Watch for selection key changes to handle deselection
watch(
  selectedKey,
  (newSelectedKey) => {
    // If selectedKey becomes empty or undefined, clear all selections
    if (!newSelectedKey || Object.keys(newSelectedKey).length === 0) {
      selectedChapterId.value = null;
      selectedItemId.value = null;
      editingItemId.value = null;
    }
  }
);

const onNodeSelect = (node: TreeNode) => {
  if (!book.value || !node.key) {
    clearSelection();
    return;
  }

  selectedKey.value = { [node.key]: { checked: true } };

  if (node.children) {
    // Chapter (has children)
    selectedChapterId.value = node.key;
    selectedItemId.value = null;
  } else {
    // Item (no children)
    const chapter = book.value.chapters.find(ch =>
      ch.items.some(item => item.id === node.key)
    );
    selectedChapterId.value = chapter?.id ?? null;
    selectedItemId.value = node.key;
  }
};

const onNodeUnselect = () => {
  clearSelection();
};

const openAddChapterDialog = () => {
  newChapterTitle.value = '';
  showAddChapterDialog.value = true;
};

const saveChapter = async () => {
  if (book.value && newChapterTitle.value) {
    await store.addChapter(book.value.id, { title: newChapterTitle.value });
    showAddChapterDialog.value = false;
    // Refresh tree data after adding chapter
    await refreshTreeData();
  }
};

// Item editing functions
const startEditingItem = () => {
  showEditItemDialog.value = true;
};

const handleItemUpdated = async (updatedItem: any) => {
  // Refresh tree data to show updated item
  await refreshTreeData();
  // Maintain selection after update
  if (updatedItem && selectedItemId.value === updatedItem.id) {
    selectedKey.value = { [updatedItem.id]: { checked: true } };
  }
};

// Delete functionality
const onNodeContextMenu = (event: any) => {
  // Context menu functionality can be added here if needed
  console.log('Context menu for node:', event.node);
};



const confirmDeleteBook = () => {
  if (!book.value) return;

  nodeToDelete.value = {
    key: book.value.id,
    label: book.value.title,
    data: 'book'
  } as TreeNode;

  deleteMessage.value = `Are you sure you want to delete the book "${book.value.title}" and all its chapters and items? This action cannot be undone.`;
  showDeleteDialog.value = true;
};

const deleteSelectedItem = () => {
  if (!selectedItemId.value || !book.value) return;

  // Find the item to get its title
  const chapter = book.value.chapters.find(ch =>
    ch.items.some(item => item.id === selectedItemId.value)
  );
  const item = chapter?.items.find(item => item.id === selectedItemId.value);

  if (!item) return;

  nodeToDelete.value = {
    key: selectedItemId.value,
    label: item.title,
    data: 'item'
  } as TreeNode;

  deleteMessage.value = `Are you sure you want to delete the item "${item.title}"?`;
  showDeleteDialog.value = true;
};

const deleteSelectedChapter = () => {
  if (!selectedChapterId.value || !book.value) return;

  // Find the chapter to get its title
  const chapter = book.value.chapters.find(ch => ch.id === selectedChapterId.value);

  if (!chapter) return;

  nodeToDelete.value = {
    key: selectedChapterId.value,
    label: chapter.title,
    data: 'chapter',
    children: chapter.items.length > 0 ? [] : undefined // Indicate if it has items
  } as TreeNode;

  if (chapter.items.length > 0) {
    deleteMessage.value = `Are you sure you want to delete the chapter "${chapter.title}" and all its ${chapter.items.length} items? This action cannot be undone.`;
  } else {
    deleteMessage.value = `Are you sure you want to delete the chapter "${chapter.title}"?`;
  }

  showDeleteDialog.value = true;
};

const performDelete = async () => {
  if (!nodeToDelete.value) {
    return;
  }

  const node = nodeToDelete.value;

  try {
    if (node.data === 'book') {
      // It's a book
      await store.deleteBook(node.key);
      // Navigate to home since the book is deleted
      router.push('/');
    } else if (node.data === 'item' || (!node.children && node.data !== 'book')) {
      // It's an item (either explicitly marked or inferred from lack of children)
      if (!book.value) return;
      const chapter = book.value.chapters.find(ch =>
        ch.items.some(item => item.id === node.key)
      );

      if (chapter) {
        await store.deleteItem(book.value.id, chapter.id, node.key);

        // Clear selection if deleted item was selected
        if (selectedItemId.value === node.key) {
          clearSelection();
        }
      }
    } else if (node.data === 'chapter' || node.children !== undefined) {
      // It's a chapter
      if (!book.value) return;
      await store.deleteChapter(book.value.id, node.key);

      // Clear selection if deleted chapter was selected
      if (selectedChapterId.value === node.key) {
        clearSelection();
      }
    }

    showDeleteDialog.value = false;
    nodeToDelete.value = null;

    // Refresh tree data after deletion (except for book deletion since we navigate away)
    if (node.data !== 'book') {
      await refreshTreeData();
    }
  } catch (error) {
    console.error('Error deleting:', error);
    // You could show an error message here
  }
};

// Item dialog methods
const openItemDialog = () => {
  showItemDialog.value = true;
};

const handleItemCreated = (item: any) => {
  // Refresh tree data and select the new item
  refreshTreeData();
  // Optionally select the new item
  selectedItemId.value = item.id;
};

const getSelectedChapterTitle = () => {
  if (!selectedChapterId.value || !book.value) return '';
  const chapter = book.value.chapters.find(ch => ch.id === selectedChapterId.value);
  return chapter?.title || '';
};
</script>