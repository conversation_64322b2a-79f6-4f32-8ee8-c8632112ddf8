import { defineStore } from 'pinia';
import type { Book } from '../types/book';
import type { Item } from '../types/item';
import { useAuthStore } from './auth';
import { collection, onSnapshot, getDocs } from 'firebase/firestore';
import { db } from '../config/firebase';
import { buildApiUrl } from '../config/api';

interface BookState {
  books: Book[];
  selectedBookId: string | null;
  loading: boolean;
  error: string | null;
  isRealTimeEnabled: boolean;
  unsubscribers: (() => void)[];
  // Loading states for individual actions
  actionLoading: {
    addingBook: boolean;
    addingChapter: boolean;
    addingItem: boolean;
    updatingItem: boolean;
    deletingItem: boolean;
    deletingChapter: boolean;
    deletingBook: boolean;
  };
}

export const useBookStore = defineStore('book', {
  state: (): BookState => ({
    books: [],
    selectedBookId: null,
    loading: false,
    error: null,
    isRealTimeEnabled: false,
    unsubscribers: [],
    actionLoading: {
      addingBook: false,
      addingChapter: false,
      addingItem: false,
      updatingItem: false,
      deletingItem: false,
      deletingChapter: false,
      deletingBook: false,
    },
  }),

  getters: {
    selectedBook: (state) => {
      return state.books.find(book => book.id === state.selectedBookId) || null;
    },
    getBook: (state) => (id: string) => {
      return state.books.find(book => book.id === id) || null;
    },
  },

  actions: {
    // Disable real-time listeners
    disableRealTimeUpdates() {
      console.log('🔇 Disabling real-time updates...');
      this.unsubscribers.forEach(unsubscribe => unsubscribe());
      this.unsubscribers = [];
      this.isRealTimeEnabled = false;
    },

    // Clean up listeners
    cleanup() {
      console.log('🧹 Cleaning up book store...');
      this.disableRealTimeUpdates();
    },

    // Initialize the store - non-blocking approach
    async initializeStore() {
      console.log('🚀 Initializing book store (non-blocking)...');
      const authStore = useAuthStore();

      // Don't wait for auth - let the auth state change handler deal with loading
      if (authStore.initialized) {
        // Auth is already ready, trigger the auth state change handler
        console.log('📚 Auth already initialized, triggering auth state change...');
        await this.onAuthStateChange(authStore.isAuthenticated);
      } else {
        console.log('⏳ Auth not ready yet, will load data when auth state changes');
        // The onAuthStateChange handler will load data when auth is ready
      }
    },

    // Called when user authentication state changes
    async onAuthStateChange(isAuthenticated: boolean) {
      console.log(`🔐 Auth state changed: ${isAuthenticated ? 'authenticated' : 'not authenticated'}`);

      if (isAuthenticated) {
        // User signed in - load books from Firestore
        console.log('📚 User signed in, loading books from Firestore...');
        // Clean up any existing listeners first
        this.cleanup();
        await this.loadBooksFromFirestore();
      } else {
        // User signed out - clear books and load static fallback
        console.log('🔒 User signed out, clearing books and loading static fallback...');
        this.cleanup();
        await this.loadBooksFromStatic();
      }
    },

    // Load books directly from Firestore with real-time updates (optimized)
    async loadBooksFromFirestore() {
      console.log('📚 Loading books from Firestore (optimized)...');
      this.loading = true;
      this.error = null;

      try {
        // Set up real-time listener for books collection
        const booksCollection = collection(db, 'books');

        // Use onSnapshot for real-time updates
        const unsubscribe = onSnapshot(booksCollection, (snapshot) => {
          console.log('📡 Books collection changed, updating books...');

          // Clear existing unsubscribers for chapters/items before setting up new ones
          this.unsubscribers.forEach(unsub => {
            if (unsub !== unsubscribe) { // Don't unsubscribe the books listener
              unsub();
            }
          });
          this.unsubscribers = [unsubscribe]; // Keep only the books listener

          // Process the snapshot directly for better real-time updates
          const books: Book[] = [];
          snapshot.docs.forEach((bookDoc) => {
            const bookData = bookDoc.data();
            books.push({
              id: bookDoc.id,
              title: bookData.title || '',
              description: bookData.description || '',
              chapters: [] // Start with empty chapters, load them progressively
            });
          });

          // Update books immediately
          this.books = books;
          this.loading = false;
          console.log(`✅ Real-time update: ${books.length} books loaded`);

          // Set up real-time listeners for chapters and items immediately
          if (books.length > 0) {
            this.loadChaptersAndItems(books);
          }
        }, (error) => {
          console.error('❌ Firestore listener error:', error);
          // Fall back to static file if Firestore fails
          this.loadBooksFromStatic();
        });

        // Store the unsubscriber
        this.unsubscribers.push(unsubscribe);

        // The onSnapshot listener will handle initial data loading

      } catch (error) {
        console.error('❌ Failed to set up Firestore listener:', error);
        // Fall back to static file
        this.loadBooksFromStatic();
      }
    },

    // Load books first (fast), then chapters/items progressively
    async loadCompleteBookData() {
      console.log('🔄 Starting optimized book data loading...');
      this.loading = true;
      this.error = null;

      try {
        // First, load just the books quickly
        const books: Book[] = [];
        const booksCollection = collection(db, 'books');
        console.log('📖 Getting books collection...');
        const booksSnapshot = await getDocs(booksCollection);
        console.log(`📖 Found ${booksSnapshot.docs.length} book documents`);

        // Load books first to show them immediately
        for (const bookDoc of booksSnapshot.docs) {
          const bookData = bookDoc.data();
          books.push({
            id: bookData.id,
            title: bookData.title,
            description: bookData.description,
            chapters: [] // Start with empty chapters, load them progressively
          });
        }

        // Update UI immediately with books (empty chapters)
        this.books = books;
        this.loading = false;
        console.log(`✅ Loaded ${books.length} books (chapters loading in background)`);

        // Now load chapters and items in the background
        await this.loadChaptersAndItems(books);

      } catch (error) {
        console.error('❌ Failed to load book data:', error);
        this.error = String(error);
        this.loading = false;
        // Fall back to static file if Firestore fails
        console.log('🔄 Falling back to static file...');
        await this.loadBooksFromStatic();
      }
    },

    // Set up real-time listeners for chapters and items
    async loadChaptersAndItems(books: Book[]) {
      console.log('📑 Setting up real-time listeners for chapters and items...');

      try {
        for (const book of books) {
          // Set up real-time listener for chapters
          const chaptersRef = collection(db, 'books', book.id, 'chapters');

          const unsubscribeChapters = onSnapshot(chaptersRef, (chaptersSnapshot) => {
            console.log(`📡 Chapters changed for book ${book.id} (${book.title})`);

            // Clear existing chapters for this book
            book.chapters = [];

            // Process each chapter synchronously
            chaptersSnapshot.docs.forEach((chapterDoc) => {
              const chapterData = chapterDoc.data();
              const chapter = {
                id: chapterDoc.id,
                title: chapterData.title || '',
                items: [] as Item[]
              };

              // Add chapter to book immediately
              book.chapters.push(chapter);

              // Set up real-time listener for items in this chapter
              const itemsRef = collection(db, 'books', book.id, 'chapters', chapter.id, 'items');

              const unsubscribeItems = onSnapshot(itemsRef, (itemsSnapshot) => {
                console.log(`📡 Items changed for chapter ${chapter.id} (${chapter.title})`);

                // Clear existing items for this chapter
                chapter.items = [];

                // Process each item
                itemsSnapshot.docs.forEach((itemDoc) => {
                  const itemData = itemDoc.data();
                  chapter.items.push({
                    id: itemDoc.id,
                    ...itemData
                  } as Item);
                });

                // Sort items by order if available
                chapter.items.sort((a, b) => {
                  const aOrder = itemsSnapshot.docs.find(doc => doc.id === a.id)?.data()?.order || 0;
                  const bOrder = itemsSnapshot.docs.find(doc => doc.id === b.id)?.data()?.order || 0;
                  return aOrder - bOrder;
                });

                console.log(`✅ Updated ${chapter.items.length} items for chapter ${chapter.title}`);

                // Trigger reactivity
                this.books = [...this.books];
              }, (error) => {
                console.error(`❌ Items listener error for chapter ${chapter.id}:`, error);
              });

              // Store the items unsubscriber
              this.unsubscribers.push(unsubscribeItems);
            });

            // Sort chapters by order if available
            book.chapters.sort((a, b) => {
              const aOrder = chaptersSnapshot.docs.find(doc => doc.id === a.id)?.data()?.order || 0;
              const bOrder = chaptersSnapshot.docs.find(doc => doc.id === b.id)?.data()?.order || 0;
              return aOrder - bOrder;
            });

            console.log(`✅ Updated ${book.chapters.length} chapters for book ${book.title}`);

            // Trigger reactivity
            this.books = [...this.books];
          }, (error) => {
            console.error(`❌ Chapters listener error for book ${book.id}:`, error);
          });

          // Store the chapters unsubscriber
          this.unsubscribers.push(unsubscribeChapters);
        }

        console.log(`✅ Real-time listeners set up for ${books.length} books`);

      } catch (error) {
        console.error('❌ Failed to set up real-time listeners:', error);
        // Fall back to static loading
        await this.loadChaptersAndItemsStatic(books);
      }
    },

    // Fallback static loading method
    async loadChaptersAndItemsStatic(books: Book[]) {
      console.log('📑 Loading chapters and items statically (fallback)...');

      try {
        for (const book of books) {
          const bookRef = collection(db, 'books', book.id, 'chapters');
          const chaptersSnapshot = await getDocs(bookRef);

          for (const chapterDoc of chaptersSnapshot.docs) {
            const chapterData = chapterDoc.data();
            const chapter = {
              id: chapterDoc.id,
              title: chapterData.title || '',
              items: [] as Item[]
            };

            // Load items for this chapter
            const itemsRef = collection(db, 'books', book.id, 'chapters', chapter.id, 'items');
            const itemsSnapshot = await getDocs(itemsRef);

            for (const itemDoc of itemsSnapshot.docs) {
              const itemData = itemDoc.data();
              chapter.items.push({
                id: itemDoc.id,
                ...itemData
              } as Item);
            }

            book.chapters.push(chapter);
          }
        }

        // Update the store with complete data
        this.books = [...books];
        console.log(`✅ Static loading complete - loaded chapters and items`);

      } catch (error) {
        console.error('❌ Failed to load chapters/items statically:', error);
      }
    },

    // Fallback method for unauthenticated users
    async loadBooksFromStatic() {
      console.log('📄 Setting empty books for unauthenticated user...');
      this.books = [];
      this.loading = false;
      this.error = null;
      console.log('✅ Set empty books array for unauthenticated user');
    },

    // Book selection methods
    selectBook(bookId: string | null) {
      this.selectedBookId = bookId;
    },

    // Add book method
    async addBook(book: { title: string; description: string }) {
      this.actionLoading.addingBook = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl('/api/books'), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(book),
        });

        if (!response.ok) {
          throw new Error(`Failed to add book: ${response.status}`);
        }

        const newBook = await response.json();
        // Don't manually add to local state - let the real-time listener handle it
        return newBook;
      } catch (error) {
        console.error('Error adding book:', error);
        // Fallback to local state only
        const bookCount = this.books.length + 1;
        const fallbackBook = {
          id: `book${bookCount}`,
          title: book.title,
          description: book.description,
          chapters: []
        };
        this.books.push(fallbackBook);
        return fallbackBook;
      } finally {
        this.actionLoading.addingBook = false;
      }
    },

    // Add chapter method
    async addChapter(bookId: string, chapter: { title: string }) {
      this.actionLoading.addingChapter = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl(`/api/books/${bookId}/chapters`), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(chapter),
        });

        if (!response.ok) {
          throw new Error(`Failed to add chapter: ${response.status}`);
        }

        const newChapter = await response.json();
        // Don't manually add to local state - let the real-time listener handle it
        return newChapter;
      } catch (error) {
        console.error('Error adding chapter:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        if (book) {
          const chapterCount = book.chapters.length + 1;
          const fallbackChapter = {
            id: `chapter${chapterCount}`,
            title: chapter.title,
            items: []
          };
          book.chapters.push(fallbackChapter);
          return fallbackChapter;
        }
        throw error;
      } finally {
        this.actionLoading.addingChapter = false;
      }
    },

    // Add item method
    async addItem(bookId: string, chapterId: string, item: Partial<Item>) {
      this.actionLoading.addingItem = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl(`/api/books/${bookId}/chapters/${chapterId}/items`), {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(item),
        });

        if (!response.ok) {
          throw new Error(`Failed to add item: ${response.status}`);
        }

        const newItem = await response.json();
        // Don't manually add to local state - let the real-time listener handle it
        return newItem;
      } catch (error) {
        console.error('Error adding item:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemCount = chapter.items.length + 1;
          const fallbackItem: Item = {
            id: `item${itemCount}`,
            title: item.title || 'Untitled Item',
            type: item.type || 'text',
            question: item.question,
            options: item.options,
            correctAnswer: item.correctAnswer,
            timedAnswer: item.timedAnswer,
            revealTimeSeconds: item.revealTimeSeconds,
            testQuestions: item.testQuestions,
          };
          chapter.items.push(fallbackItem);
          return fallbackItem;
        }
        throw error;
      } finally {
        this.actionLoading.addingItem = false;
      }
    },

    // Update item method
    async updateItem(bookId: string, chapterId: string, itemId: string, item: Partial<Item>) {
      this.actionLoading.updatingItem = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl(`/api/books/${bookId}/chapters/${chapterId}/items/${itemId}`), {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            ...headers,
          },
          body: JSON.stringify(item),
        });

        if (!response.ok) {
          throw new Error(`Failed to update item: ${response.status}`);
        }

        const updatedItem = await response.json();
        // Don't manually update local state - let the real-time listener handle it
        return updatedItem;
      } catch (error) {
        console.error('Error updating item:', error);
        // Fallback to local state only
        const book = this.books.find(b => b.id === bookId);
        const chapter = book?.chapters.find(c => c.id === chapterId);
        if (chapter) {
          const itemIndex = chapter.items.findIndex(i => i.id === itemId);
          if (itemIndex !== -1) {
            chapter.items[itemIndex] = { ...chapter.items[itemIndex], ...item };
          }
        }
        throw error;
      } finally {
        this.actionLoading.updatingItem = false;
      }
    },

    // Delete item method
    async deleteItem(bookId: string, chapterId: string, itemId: string) {
      this.actionLoading.deletingItem = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl(`/api/books/${bookId}/chapters/${chapterId}/items/${itemId}`), {
          method: 'DELETE',
          headers: headers,
        });

        if (!response.ok) {
          throw new Error(`Failed to delete item: ${response.status}`);
        }

        // Don't manually update local state - let the real-time listener handle it
      } catch (error) {
        console.error('Error deleting item:', error);
        throw error;
      } finally {
        this.actionLoading.deletingItem = false;
      }
    },

    // Delete chapter method
    async deleteChapter(bookId: string, chapterId: string) {
      this.actionLoading.deletingChapter = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl(`/api/books/${bookId}/chapters/${chapterId}`), {
          method: 'DELETE',
          headers: headers,
        });

        if (!response.ok) {
          throw new Error(`Failed to delete chapter: ${response.status}`);
        }

        // Don't manually update local state - let the real-time listener handle it
      } catch (error) {
        console.error('Error deleting chapter:', error);
        throw error;
      } finally {
        this.actionLoading.deletingChapter = false;
      }
    },

    // Delete book method
    async deleteBook(bookId: string) {
      this.actionLoading.deletingBook = true;
      try {
        const authStore = useAuthStore();
        const headers = await authStore.getAuthHeaders();
        const response = await fetch(buildApiUrl(`/api/books/${bookId}`), {
          method: 'DELETE',
          headers: headers,
        });

        if (!response.ok) {
          throw new Error(`Failed to delete book: ${response.status}`);
        }

        // Don't manually update local state - let the real-time listener handle it
        // But clear selection if deleted book was selected
        if (this.selectedBookId === bookId) {
          this.selectedBookId = null;
        }
      } catch (error) {
        console.error('Error deleting book:', error);
        throw error;
      } finally {
        this.actionLoading.deletingBook = false;
      }
    },
  }
});
